/* Global App Styles with Tamil Cultural Typography */
.App {
  text-align: center;
  min-height: 100vh;
  height: 100vh;
  background: url('./assets/laptop/backgrounds/landing-laptop.jpg') center 71px no-repeat fixed;
  background-size: cover; /* Use cover to maintain aspect ratio and avoid stretching */
  background-position: center 71px; /* Start exactly below navigation header + golden bar */
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-color: #f8f6f0; /* Fallback color */
  color: #333333; /* Updated to Tamil cultural body text color */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  overflow-x: hidden;
  width: 100vw;
  max-width: 100vw;
  line-height: 1.6;
  padding-top: 71px; /* Push content below navigation */
}

/* iOS Safari fix for background-attachment: fixed */
@supports (-webkit-touch-callout: none) {
  .App {
    background-attachment: scroll !important;
    background-size: cover !important; /* Use cover for iOS Safari too */
    background-position: center 71px !important;
    background-repeat: no-repeat !important;
    width: 100vw !important;
    height: 100vh !important;
    min-height: 100vh !important;
    min-height: 100dvh !important; /* Dynamic viewport height for iOS */
    /* REMOVE iOS transforms that interfere with fixed positioning */
    -webkit-transform: none !important;
    transform: none !important;
    will-change: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* Use mobile background for iOS devices */
  @media (max-width: 768px) {
    .App {
      background-image: url('./assets/mobile/backgrounds/landing-mobile.jpg') !important;
      background-size: 100vw calc(100vh - 191px) !important;
      background-position: center 71px !important;
      background-attachment: scroll !important;
    }
  }
}

/* Remove overlay as we now have light background */
.App::before {
  display: none;
}

/* Ensure proper scroll behavior for navigation */
html {
  scroll-behavior: auto; /* Disable smooth scrolling to allow instant scroll to top */
}

/* Reset scroll position on route changes */
html, body {
  scroll-behavior: auto;
}

/* Prevent scroll restoration interference */
.App {
  scroll-behavior: auto;
}

/* Mobile Background (768px and below) */
@media (max-width: 768px) {
  .App {
    background-image: url('./assets/mobile/backgrounds/landing-mobile.jpg') !important;
    background-size: 100vw calc(100vh - 191px) !important;
    background-position: center 71px !important;
    background-attachment: scroll !important; /* Use scroll for better mobile performance */
    background-repeat: no-repeat !important;
    min-height: 100vh !important;
    min-height: 100dvh !important; /* Dynamic viewport height for mobile */
  }
}

/* Laptop screens (769px to 1919px) - Optimize background for laptops */
@media (min-width: 769px) and (max-width: 1919px) {
  .App {
    background-image: url('./assets/laptop/backgrounds/landing-laptop.jpg') !important;
    background-size: cover !important; /* Maintain aspect ratio, avoid stretching */
    background-position: center 71px !important;
    background-attachment: fixed !important;
    background-repeat: no-repeat !important;
  }
}

/* Small laptop screens (769px to 1366px) - Common laptop resolutions */
@media (min-width: 769px) and (max-width: 1366px) {
  .App {
    background-size: cover !important; /* Ensure no stretching on smaller laptops */
    background-position: center 71px !important;
  }
}

/* Large laptop screens (1367px to 1919px) - Larger laptop resolutions */
@media (min-width: 1367px) and (max-width: 1919px) {
  .App {
    background-size: cover !important; /* Maintain quality on larger laptops */
    background-position: center 71px !important;
  }
}

/* 24-inch Monitor Background (1920px and above) */
@media (min-width: 1920px) {
  .App {
    background-image: url('./assets/monitor/backgrounds/landing-monitor.jpg') !important;
    background-size: cover !important; /* Use cover to avoid stretching on monitors too */
    background-position: center 71px !important; /* Start exactly below navigation header + golden bar */
    background-attachment: fixed !important;
    background-repeat: no-repeat !important;
  }
}

/* Global mobile responsiveness */
* {
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Prevent horizontal scrolling on mobile */
@media (max-width: 768px) {
  .App {
    overflow-x: hidden;
    width: 100vw;
    max-width: 100vw;
    height: 100vh;
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
    /* REMOVE transform that interferes with fixed positioning */
    transform: none !important;
    -webkit-transform: none !important;
    will-change: auto !important;
  }

  * {
    max-width: 100%;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  iframe {
    max-width: 100%;
  }
}

/* Additional iOS-specific fixes */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 1) {
  .App {
    /* Ensure background works on iOS Safari */
    background-size: 100vw calc(100vh - 191px) !important;
    -webkit-background-size: 100vw calc(100vh - 191px) !important;
    background-position: center 71px !important;
    background-repeat: no-repeat !important;
    background-attachment: scroll !important;
    width: 100vw !important;
    height: 100vh !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
    /* Use mobile-specific background image */
    background-image: url('./assets/mobile/backgrounds/landing-mobile.jpg') !important;
    /* REMOVE transforms that interfere with fixed positioning */
    -webkit-transform: none !important;
    transform: none !important;
    /* REMOVE hardware acceleration that interferes with fixed elements */
    will-change: auto !important;
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
  }
}

/* Extra mobile fixes for very small screens */
@media (max-width: 480px) {
  .App {
    background-size: 100vw calc(100vh - 191px) !important;
    background-position: center 71px !important;
    background-attachment: scroll !important;
    background-repeat: no-repeat !important;
    width: 100vw !important;
    height: 100vh !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
    /* Use mobile-specific background image */
    background-image: url('./assets/mobile/backgrounds/landing-mobile.jpg') !important;
    /* REMOVE transforms that interfere with fixed positioning */
    -webkit-transform: none !important;
    transform: none !important;
    will-change: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
}

/* Android Chrome specific fixes */
@media (max-width: 768px) and (orientation: portrait) {
  .App {
    background-size: 100vw calc(100vh - 191px) !important;
    background-position: center 71px !important;
    background-attachment: scroll !important;
    background-repeat: no-repeat !important;
    width: 100vw !important;
    height: 100vh !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
    /* Use mobile-specific background image */
    background-image: url('./assets/mobile/backgrounds/landing-mobile.jpg') !important;
    /* Prevent background jumping during scroll */
    -webkit-overflow-scrolling: touch !important;
  }
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #00796B; /* Updated to Tamil cultural teal */
  transition: color 0.3s ease;
}

.App-link:hover {
  color: #DAA520; /* Tamil gold on hover */
}

/* Global Tamil Cultural Typography Styles */
h1, h2, h3, .heading-primary {
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  color: #4B0000; /* Deep Maroon */
  font-weight: 700;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

h1 {
  font-size: 3rem;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

h2 {
  font-size: 2.5rem;
  letter-spacing: 0.8px;
}

h3 {
  font-size: 2rem;
  letter-spacing: 0.6px;
}

h4, h5, h6, .heading-secondary {
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  color: #1E1E1E; /* Dark Charcoal */
  font-weight: 600;
  letter-spacing: 0.3px;
  line-height: 1.3;
}

p, .body-text {
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: #333333; /* Dark Gray */
  font-weight: 400;
  line-height: 1.7;
  font-size: 1rem;
}

/* Tamil Cultural Accent Classes */
.accent-gold {
  color: #DAA520 !important; /* Traditional Gold */
}

.accent-red {
  color: #B22222 !important; /* Tamil Red */
}

.accent-teal {
  color: #00796B !important; /* Peacock Teal */
}

/* Button Styling with Tamil Colors */
.btn-primary {
  background: linear-gradient(135deg, #DAA520, #B8860B);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #B8860B, #9A7209);
  transform: translateY(-2px);
}

.btn-secondary {
  background: linear-gradient(135deg, #00796B, #004D40);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #004D40, #00251A);
  transform: translateY(-2px);
}

.btn-accent {
  background: linear-gradient(135deg, #B22222, #8B0000);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.btn-accent:hover {
  background: linear-gradient(135deg, #8B0000, #660000);
  transform: translateY(-2px);
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
