/* Import Google Fonts for Tamil Cultural Typography */
@import url('https://fonts.googleapis.com/css2?family=Mulish:wght@400;500;600;700&family=Noto+Serif+Tamil:wght@400;500;600;700&family=Merriweather:wght@400;700&display=swap');

body {
  margin: 0;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333333; /* Tamil cultural body text color */
  line-height: 1.6;
  /* iOS Safari optimizations */
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Global Typography Classes for Tamil Cultural Theme */
.tamil-heading {
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  color: #4B0000; /* Deep Maroon */
  font-weight: 700;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.tamil-subheading {
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  color: #1E1E1E; /* Dark Charcoal */
  font-weight: 600;
  letter-spacing: 0.3px;
}

.tamil-body-text {
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: #333333; /* Dark Gray */
  font-weight: 400;
  line-height: 1.7;
}

.tamil-accent-gold {
  color: #DAA520; /* Traditional Gold */
}

.tamil-accent-red {
  color: #B22222; /* Tamil Red */
}

.tamil-accent-teal {
  color: #00796B; /* Peacock Teal */
}

.tamil-overlay {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* iOS Safari specific optimizations */
@supports (-webkit-touch-callout: none) {
  /* Fix for iOS Safari background issues */
  html, body {
    height: 100%;
    -webkit-overflow-scrolling: touch;
  }

  /* Improve touch responsiveness */
  button, a, [role="button"] {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Fix for iOS Safari viewport issues */
  * {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}
