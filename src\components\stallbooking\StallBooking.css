/* Stall Booking Page Styles with Tamil Cultural Typography */
.stallbooking-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 86px 20px 200px;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.7;
  margin-bottom: 20px;
  color: #333333; /* Tamil cultural body text */
}

/* Page Heading with Tamil Cultural Styling */
.stallbooking-heading {
  font-size: 2.4rem;
  font-weight: 700;
  color: #4B0000; /* Deep Maroon */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: center;
  margin-bottom: 40px;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

/* Main Content Section */
.main-content-section {
  border-radius: 15px;
  padding: 0px 30px 0 30px;
}

/* Detailed Description Section */
.detailed-description-section {
  margin-bottom: 50px;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.content-card {
  border-radius: 15px;
  padding: 0px 30px 0px 30px;
}

.main-content {
  font-size: 1.2rem;
  color: #000000; /* Black text */
  text-align: justify;
  margin: 0;
  font-weight: 500;
  line-height: 1.8;
  
}

.sub-content {
  font-size: 1.2rem;
  color: #000000; /* Black text */
  text-align: justify;
  margin: 0;
  font-weight: 500;
  line-height: 1.8;
  
}

.call-to-action {
  background: transparent;
  border-radius: 0;
  margin-top: 20px;
}

.cta-text {
  color: #000000; /* Traditional Gold */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  text-transform: none;
  letter-spacing: 0.3px;
  font-style: italic;
  text-shadow:
    1px 1px 1px rgba(63, 22, 22, 0.6),
    -1px -1px 1px rgba(255, 255, 255, 0.6),
    1px -1px 1px rgba(255, 255, 255, 0.6),
    -1px 1px 1px rgba(255, 255, 255, 0.6),
    0 0 1px rgba(255, 255, 255, 0.1); /* White glow effect for readability */
}

/* Detailed Description Content Styles */
.detailed-description {
  font-size: 1.2rem;
  color: #000000; /* Black text */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  margin-bottom: 20px;
  font-weight: 500;
  line-height: 1.7;
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -2px 0 0 #ffffff,
    2px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
}

.organization-info {
  font-size: 1.1rem;
  color: #000000; /* Black text */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  margin-bottom: 20px;
  font-weight: 500;
  line-height: 1.7;
  text-shadow:
    1px 1px 1px rgba(255, 255, 255, 0.6),
    -1px -1px 1px rgba(255, 255, 255, 0.6),
    1px -1px 1px rgba(255, 255, 255, 0.6),
    -1px 1px 1px rgba(255, 255, 255, 0.6),
    0 0 1px rgba(255, 255, 255, 0.1); /* White glow effect for readability */
}

.invitation-text {
  font-size: 1.1rem;
  color: #000000; /* Black text */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  margin-bottom: 25px;
  font-weight: 500;
  line-height: 1.7;
  text-shadow:
    1px 1px 1px rgba(255, 255, 255, 0.6),
    -1px -1px 1px rgba(255, 255, 255, 0.6),
    1px -1px 1px rgba(255, 255, 255, 0.6),
    -1px 1px 1px rgba(255, 255, 255, 0.6),
    0 0 1px rgba(255, 255, 255, 0.1); /* White glow effect for readability */
}

.contact-info-section {
  text-align: center;
  margin-top: 20px;
}

.contact-text {
  font-size: 1.1rem;
  color: #000000; /* Black text */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  margin-bottom: 15px;
  font-weight: 500;
  padding: 12px 18px;
  border-radius: 8px;
  display: block;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
 
}

.contact-number {
  font-size: 1.3rem;
  color: #000000; /* Black text */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-weight: 700;
  margin: 0 auto;
  padding: 12px 18px;
  border-radius: 8px;
  display: block;
  width: fit-content;
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -2px 0 0 #ffffff,
    2px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
}

/* Section Titles with Tamil Cultural Styling */
.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: #4B0000; /* Deep Maroon */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: center;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* Theme Section */
.theme-section {
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 40px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.theme-description {
  font-size: 1.2rem;
  color: #1E1E1E; /* Dark Charcoal */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-weight: 500;
  font-style: italic;
  background: rgba(255, 255, 255, 0.8);
  padding: 15px 25px;
  border-radius: 8px;
  display: inline-block;
  border: 2px solid #4B0000; /* Deep Maroon */
  letter-spacing: 0.3px;
}

/* Business Types Section */
.business-types-section {
  padding: 40px 30px;
  border-radius: 15px;
  margin-bottom: 40px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.business-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.business-item {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  padding: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
  font-weight: 600;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.business-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(116, 185, 255, 0.4);
}

.business-icon {
  font-size: 1.5rem;
}

.business-name {
  font-size: 1rem;
}

/* Contact Section */
.contact-section {
  background: transparent;
  border-radius: 0;
  margin-bottom: 40px;
  text-align: center;
  box-shadow: none;
  padding: 0;
}

.contact-details {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  margin-top: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #00b894, #00a085);
  padding: 15px 25px;
  border-radius: 25px;
  color: white;
  font-weight: 600;
  transition: transform 0.3s ease;
}

.contact-item:hover {
  transform: scale(1.05);
}

.contact-icon {
  font-size: 1.2rem;
}

.contact-text {
  font-size: 1rem;
  padding: 10px 15px;
}

.contact-number {
  font-size: 1.1rem;
  padding: 10px 15px;
}

/* Partners Section */
.partners-section {
  padding: 30px 40px 40px 40px;
  border-radius: 15px;
  margin-bottom: 40px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.partners-description {
  text-align: center;
  font-size: 1.1rem;
  color: #34495e;
  margin-bottom: 30px;
  font-style: italic;
}

.partners-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.partner-card {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 25px;
  border-radius: 15px;
  text-align: center;
  color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.partner-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.partner-logo {
  margin-bottom: 15px;
}

.partner-logo img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: white;
  padding: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.partner-name {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.partner-tier {
  font-size: 1rem;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 5px;
}

.partner-category {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

/* Form Section */
.form-section {
  background: transparent;
  padding: 40px 0;
  border-radius: 0;
  box-shadow: none;
}

.form-container {
  max-width: 1000px;
  margin: 0 auto;
  border-radius: 0;
  overflow: visible;
  box-shadow: none;
  background: transparent;
  border: none;
  width: 100%;
  position: relative;
}

.google-form-iframe {
  min-height: 13636px;
  height: 13636px;
  overflow: hidden;
  background: transparent !important;
  border: none !important;
  display: block;
  width: 640px;
  max-width: 100%;
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stallbooking-container {
    padding: 60px 15px 180px;
  }

  .stallbooking-heading {
    font-size: 2.2rem;
  }

  .section-title {
    font-size: 1.6rem;
    color: #4B0000;
  }

  .content-card {
    padding: 25px;
  }

  .main-content {
    font-size: 1.1rem;
  }

  .business-grid {
    grid-template-columns: 1fr;
  }

  .contact-details {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .partners-showcase {
    grid-template-columns: 1fr;
  }

  .form-section {
    padding: 20px 0;
    margin: 20px 0;
  }

  .form-container {
    width: 100%;
    overflow: auto;
    padding: 0;
  }

  .google-form-iframe {
    height: 8000px;
    min-height: 6000px;
    max-height: none;
    overflow: auto;
    background: transparent !important;
    border: none !important;
    scrolling: no;
    width: 100%;
    display: block;
  }
}

/* Additional styling to ensure seamless Google Form integration */
.form-container {
  position: relative;
}

/* Remove any potential scroll bars and ensure clean embedding */
.google-form-iframe {
  background-color: transparent !important;
  opacity: 1;
  -webkit-overflow-scrolling: touch;
}

/* Desktop and large screens - show full form without scroll bars */
@media (min-width: 769px) {
  .google-form-iframe {
    height: 13636px;
    min-height: 13636px;
    overflow: hidden;
  }
}

/* Tablet screens */
@media (max-width: 768px) and (min-width: 481px) {
  .google-form-iframe {
    height: 10000px;
    min-height: 8000px;
    overflow: auto;
  }
}

/* Registration form title styling - elegant text design */
.form-section .section-title {
  background: none;
  padding: 0;
  color: #4B0000;
  font-size: 1.5rem;
  border-radius: 0;
  display: block;
  margin: 0 auto 40px auto;
  text-align: center;
  width: 100%;
  position: relative;
}

/* Add decorative underline to form title */
.form-section .section-title::after {
  content: '';
  display: block;
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  margin: 15px auto 0 auto;
  border-radius: 2px;
}

/* Form fallback button styling */
.form-fallback {
  display: block !important;
  text-align: center;
  margin-top: 50px;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
}

.form-fallback p {
  color: #333333;
  font-size: 1rem;
  margin-bottom: 15px;
}

.form-direct-link {
  display: inline-block !important;
  padding: 12px 24px;
  background-color: #e67e22;
  color: white !important;
  text-decoration: none;
  border-radius: 5px;
  font-weight: bold;
  margin-top: 10px;
  transition: background-color 0.3s ease;
  font-size: 16px;
}

.form-direct-link:hover {
  background-color: #d35400;
  color: white !important;
  text-decoration: none;
}

@media (max-width: 480px) {
  .stallbooking-heading {
    font-size: 1.8rem;
  }

  .main-content,
  .sub-content {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .content-card {
    padding: 20px;
  }

  .contact-details {
    gap: 15px;
  }

  .contact-text {
    font-size: 0.95rem;
    padding: 8px 12px;
  }

  .contact-number {
    font-size: 1rem;
    padding: 8px 12px;
  }

  .business-item {
    padding: 15px;
  }

  .partner-card {
    padding: 20px;
  }

  .form-section {
    padding: 0;
  }

  .form-section {
    padding: 15px 0;
    margin: 15px 0;
  }

  .form-container {
    width: 100%;
    overflow: auto;
    padding: 0;
  }

  .google-form-iframe {
    height: 6000px;
    min-height: 5000px;
    max-height: none;
    overflow: auto;
    background: transparent !important;
    border: none !important;
    width: 100%;
    display: block;
  }

  /* Ensure form button is visible on mobile */
  .form-fallback {
    display: block !important;
    margin: 40px 10px;
    padding: 15px;
  }

  .form-direct-link {
    display: inline-block !important;
    padding: 15px 20px;
    font-size: 14px;
    width: auto;
    max-width: 90%;
    text-align: center;
  }
}
