/* Sustainability Page Styles with Tamil Cultural Typography */
.sustainability-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 86px 20px 200px; /* Added bottom padding for fixed countdown container, adjusted for new navbar height */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.7;
  margin-bottom: 20px;
  color: #333333; /* Tamil cultural body text */
}

/* Removed language toggle styles as they are no longer needed */

/* Page Heading with Tamil Cultural Styling */
.sustainability-heading {
  font-size: 2.4rem;
  font-weight: 700;
  color: #4B0000; /* Deep Maroon */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: center;
  margin-bottom: 40px;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

/* Description Section */
.sustainability-description {
  margin-bottom: 50px;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.sustainability-description p {
  font-size: 1.2rem;
  color: #000000; /* Black text */
  text-align: justify;
  margin: 0;
  font-weight: 500;
  line-height: 1.8;
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -2px 0 0 #ffffff,
    2px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
}

/* Table Section */
.table-section {
  margin-top: 40px;
  position: relative;
  z-index: 10; /* Ensure download button stays above cards */
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.table-header h2 {
  font-size: 1.8rem;
  color: #000000; /* Black text */
  margin: 0;
  font-weight: 600;
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -2px 0 0 #ffffff,
    2px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
}

/* Download Button */
.download-btn {
  background: linear-gradient(135deg, #0077ff, #0056b3);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 119, 255, 0.3);
  position: relative;
  z-index: 20; /* Ensure it stays above everything */
}

.download-btn:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 119, 255, 0.4);
}

.download-btn:active {
  transform: translateY(0);
}

/* Card-based Layout Styles */
.card-table-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); /* Improved responsive grid */
  gap: 25px;
  margin-top: 40px; /* More space from download button */
  align-items: stretch; /* Make all cards same height */
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.card-scope-section {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%; /* Make all cards same height */
  min-height: 350px; /* Reduced height for better fit with 6 cards */
  display: flex;
  flex-direction: column;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-scope-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

/* Staggered animation delays for 6 cards */
.card-scope-section:nth-child(1) {
  animation-delay: 0.1s;
}

.card-scope-section:nth-child(2) {
  animation-delay: 0.2s;
}

.card-scope-section:nth-child(3) {
  animation-delay: 0.3s;
}

.card-scope-section:nth-child(4) {
  animation-delay: 0.4s;
}

.card-scope-section:nth-child(5) {
  animation-delay: 0.5s;
}

.card-scope-section:nth-child(6) {
  animation-delay: 0.6s;
}

/* Make Emissions Avoided card more prominent with styling instead of size */
.emissions-avoided-card {
  border: 3px solid #27ae60;
  box-shadow: 0 12px 35px rgba(39, 174, 96, 0.2);
  min-height: 350px; /* Same height as others */
  position: relative;
  overflow: visible;
}

.emissions-avoided-card::before {
  content: "★ IMPORTANT";
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  z-index: 10;
}

.emissions-avoided-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 45px rgba(39, 174, 96, 0.3);
}

.card-scope-title {
  background: linear-gradient(135deg, #4B0000, #8B0000); /* Deep Maroon gradient */
  color: white;
  padding: 20px;
  font-size: 1.3rem;
  font-weight: 700;
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: center;
  margin: 0;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  height: 80px; /* Fixed height for consistent alignment */
  line-height: 1.2;
}

.importance-indicator {
  font-size: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.emissions-avoided-card .card-scope-title {
  background: linear-gradient(135deg, #00796B, #004D40); /* Peacock Teal gradient */
  font-size: 1.4rem;
  padding: 25px;
  height: 80px; /* Same fixed height as other cards */
}

.card-rows-container {
  padding: 0;
  flex: 1; /* Take remaining space */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Distribute content evenly */
}

.card-table-row {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Ensure the last row (emissions) stays at bottom */
.card-table-row:last-child {
  margin-top: auto;
}

.card-table-row:hover {
  background-color: rgba(230, 126, 34, 0.05);
}

.emissions-avoided-card .card-table-row:hover {
  background-color: rgba(39, 174, 96, 0.05);
}

.card-table-row:last-child {
  border-bottom: none;
}

.card-row-header {
  font-weight: 700;
  color: #1E1E1E; /* Dark Charcoal */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-size: 1.1rem;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #4B0000; /* Deep Maroon */
  letter-spacing: 0.3px;
}

.emissions-avoided-card .card-row-header {
  border-bottom-color: #00796B; /* Peacock Teal */
}

.card-content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.card-field {
  margin-bottom: 0;
}

.card-emissions-field {
  background: rgba(230, 126, 34, 0.05);
  padding: 10px;
  border-radius: 8px;
  border-left: 4px solid #e67e22;
}

.emissions-avoided-card .card-emissions-field {
  background: rgba(39, 174, 96, 0.05);
  border-left-color: #27ae60;
}

.card-field-label {
  font-weight: 600;
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-field-value {
  color: #333333; /* Tamil cultural body text */
  font-family: 'Inter', 'Open Sans', 'Poppins', sans-serif;
  font-size: 0.95rem;
  line-height: 1.6;
  font-weight: 500;
  word-wrap: break-word;
  text-align: justify;
}

.card-emissions-value {
  font-weight: 700;
  font-size: 1.1rem;
  color: #B22222; /* Tamil Red */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: center;
  letter-spacing: 0.5px;
}

.card-emissions-avoided {
  color: #00796B !important; /* Peacock Teal */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-size: 1.2rem;
  text-align: center;
  font-weight: 700;
  letter-spacing: 0.5px;
}

/* Large screen optimization - 1200px to 1599px */
@media (min-width: 1200px) and (max-width: 1599px) {
  .card-table-container {
    gap: 25px;
    max-width: 1400px;
    margin: 50px auto 0; /* More space from download button */
    grid-template-columns: repeat(3, 1fr); /* 3 columns for large screens */
  }

  .card-scope-section {
    min-height: 400px;
  }

  .emissions-avoided-card {
    min-height: 400px; /* Same height as others */
  }

  .card-scope-title {
    font-size: 1.4rem;
    padding: 25px;
    height: 90px; /* Fixed height for large screens */
  }

  .emissions-avoided-card .card-scope-title {
    font-size: 1.5rem;
    padding: 30px;
    height: 90px; /* Same fixed height as other cards */
  }

  .card-table-row {
    padding: 25px;
  }

  .card-row-header {
    font-size: 1.2rem;
  }

  .card-field-value {
    font-size: 1.1rem;
  }
}

/* Extra Large screens - 1600px to 1919px - 3x2 layout */
@media (min-width: 1600px) and (max-width: 1919px) {
  .sustainability-container {
    max-width: 1600px;
    padding: 80px 40px 200px;
  }

  .card-table-container {
    grid-template-columns: repeat(3, 1fr); /* 3x2 layout for better spacing */
    gap: 30px;
    max-width: 1500px;
    margin: 50px auto 0;
  }

  .card-scope-section {
    min-height: 420px;
  }

  .emissions-avoided-card {
    min-height: 420px;
  }

  .card-scope-title {
    font-size: 1.4rem;
    padding: 25px;
    height: 95px;
  }

  .emissions-avoided-card .card-scope-title {
    font-size: 1.5rem;
    padding: 30px;
    height: 95px;
  }

  .card-table-row {
    padding: 25px;
  }

  .card-row-header {
    font-size: 1.2rem;
  }

  .card-field-value {
    font-size: 1.1rem;
  }
}

/* 24-inch Monitor and Ultra-wide screens - 1920px+ */
@media (min-width: 1920px) {
  .sustainability-container {
    padding: 100px 60px 200px;
    max-width: 1800px;
    margin: 0 auto;
  }

  .sustainability-heading {
    font-size: 3.2rem;
    margin-bottom: 50px;
  }

  .sustainability-description {
    max-width: 1600px;
    margin: 0 auto 60px;
    padding: 40px;
  }

  .sustainability-description p {
    font-size: 1.3rem;
    line-height: 1.8;
  }

  .card-table-container {
    grid-template-columns: repeat(3, 1fr); /* 3x2 layout for optimal readability */
    gap: 45px;
    max-width: 1700px;
    margin: 60px auto 0;
  }

  .card-scope-section {
    min-height: 480px;
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-scope-section:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
  }

  .emissions-avoided-card {
    min-height: 480px;
    border: 3px solid #27ae60;
  }

  .emissions-avoided-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 60px rgba(39, 174, 96, 0.3);
  }

  .card-scope-title {
    font-size: 1.6rem;
    padding: 30px 25px;
    line-height: 1.3;
    height: 110px;
  }

  .emissions-avoided-card .card-scope-title {
    font-size: 1.7rem;
    padding: 35px 30px;
    height: 110px;
  }

  .card-table-row {
    padding: 30px;
  }

  .card-row-header {
    font-size: 1.3rem;
    margin-bottom: 15px;
  }

  .card-field-value {
    font-size: 1.2rem;
    line-height: 1.6;
  }

  .card-emissions-value {
    font-size: 1.4rem;
    font-weight: 700;
    text-align: center;
  }

  .card-emissions-avoided {
    font-size: 1.6rem;
    font-weight: 800;
    text-align: center;
  }

  .table-header {
    margin-bottom: 50px;
  }

  .table-header h2 {
    font-size: 2.5rem;
  }

  .download-btn {
    padding: 15px 35px;
    font-size: 1.1rem;
    border-radius: 12px;
  }
}

/* Specific optimization for 1700-1800px screens (like 1796px) */
@media (min-width: 1700px) and (max-width: 1850px) {
  .sustainability-container {
    max-width: 1650px;
    padding: 90px 50px 200px;
    margin: 0 auto;
  }

  .card-table-container {
    grid-template-columns: repeat(3, 1fr); /* 3x2 layout for optimal spacing */
    gap: 35px;
    max-width: 1550px;
    margin: 60px auto 0;
  }

  .card-scope-section {
    min-height: 440px;
    border-radius: 18px;
  }

  .emissions-avoided-card {
    min-height: 440px;
  }

  .card-scope-title {
    font-size: 1.45rem;
    padding: 28px;
    height: 100px;
    line-height: 1.2;
  }

  .emissions-avoided-card .card-scope-title {
    font-size: 1.55rem;
    padding: 32px;
    height: 100px;
  }

  .card-table-row {
    padding: 28px;
  }

  .card-row-header {
    font-size: 1.25rem;
    margin-bottom: 15px;
  }

  .card-field-value {
    font-size: 1.15rem;
    line-height: 1.6;
  }

  .card-emissions-value {
    font-size: 1.25rem;
  }

  .card-emissions-avoided {
    font-size: 1.4rem;
  }
}

/* Large Tablet and Medium Desktop - 3 cards per row */
@media (max-width: 1199px) and (min-width: 900px) {
  .card-table-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 40px;
  }

  .card-scope-section {
    min-height: 350px;
  }

  .emissions-avoided-card {
    min-height: 350px;
  }
}

/* Tablet Responsive Design - 2 cards per row */
@media (max-width: 899px) and (min-width: 600px) {
  .card-table-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-top: 40px;
  }

  .card-scope-section {
    min-height: 320px;
  }

  .emissions-avoided-card {
    min-height: 320px;
  }

  .emissions-avoided-card:hover {
    transform: translateY(-5px);
  }
}

/* Mobile Responsive Design */
@media (max-width: 599px) {
  .sustainability-container {
    padding: 80px 15px 180px;
  }

  .sustainability-heading {
    font-size: 2.2rem;
    margin-bottom: 30px;
  }

  .sustainability-description {
    padding: 20px;
    margin-bottom: 30px;
  }

  .sustainability-description p {
    font-size: 1rem;
  }

  .table-header {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .table-header h2 {
    font-size: 1.5rem;
    text-align: center;
    margin: 0;
  }

  .download-btn {
    width: auto;
    min-width: 140px;
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  /* Mobile card layout */
  .card-table-container {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 30px;
  }

  .card-scope-section {
    min-height: auto; /* Allow natural height on mobile */
  }

  .emissions-avoided-card {
    order: -1; /* Move to top on mobile */
    min-height: auto;
  }

  .emissions-avoided-card:hover {
    transform: translateY(-5px);
  }

  .card-scope-title {
    font-size: 1.2rem;
    padding: 15px;
    height: 70px; /* Fixed height for mobile */
  }

  .emissions-avoided-card .card-scope-title {
    font-size: 1.3rem;
    padding: 20px;
    height: 70px; /* Same fixed height as other cards */
  }

  .card-table-row {
    padding: 15px;
  }

  .card-row-header {
    font-size: 1rem;
    margin-bottom: 12px;
  }

  .card-field-value {
    font-size: 0.95rem;
  }

  .card-emissions-value {
    font-size: 1rem;
    text-align: center;
  }

  .card-emissions-avoided {
    font-size: 1.1rem;
    text-align: center;
  }
}

/* Small Mobile Responsive Design */
@media (max-width: 480px) {
  .sustainability-heading {
    font-size: 1.8rem;
    letter-spacing: 1px;
  }

  .sustainability-description p {
    font-size: 0.95rem;
    line-height: 1.6;
  }

  .table-header {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .table-header h2 {
    text-align: center;
    margin: 0;
  }

  .download-btn {
    width: auto;
    max-width: 160px;
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .card-scope-title {
    font-size: 1.1rem;
    padding: 12px;
  }

  .emissions-avoided-card .card-scope-title {
    font-size: 1.2rem;
    padding: 15px;
  }

  .card-table-row {
    padding: 12px;
  }

  .card-row-header {
    font-size: 0.95rem;
    margin-bottom: 10px;
  }

  .card-field-value {
    font-size: 0.9rem;
  }

  .card-emissions-value {
    font-size: 0.95rem;
    text-align: center;
  }

  .card-emissions-avoided {
    font-size: 1rem;
    text-align: center;
  }
}

/* Print Styles */
@media print {
  .download-btn {
    display: none;
  }

  .sustainability-container {
    padding: 20px;
  }

  .card-table-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .card-scope-section {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
  }

  .emissions-avoided-card {
    transform: none;
    border: 2px solid #27ae60;
  }
}
